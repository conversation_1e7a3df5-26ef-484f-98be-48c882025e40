<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>浏览器全屏功能测试</title>
    <style>
        .container {
            padding: 20px;
            border: 2px solid #ccc;
            margin: 20px;
            transition: all 0.3s ease;
        }
        
        .fullscreen-mode {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            z-index: 9999 !important;
            background: white !important;
            overflow: hidden !important;
            margin: 0 !important;
            padding: 20px !important;
            border: none !important;
        }
        
        .fullscreen-toolbar {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10000;
            background: rgba(255, 255, 255, 0.95);
            padding: 8px 12px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .table-container {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .fullscreen-table {
            height: calc(100vh - 80px) !important;
            border-radius: 0 !important;
        }
        
        /* 浏览器全屏时的样式 */
        :fullscreen .fullscreen-mode,
        :-webkit-full-screen .fullscreen-mode,
        :-moz-full-screen .fullscreen-mode,
        :-ms-fullscreen .fullscreen-mode {
            width: 100% !important;
            height: 100% !important;
        }
        
        :fullscreen .fullscreen-table,
        :-webkit-full-screen .fullscreen-table,
        :-moz-full-screen .fullscreen-table,
        :-ms-fullscreen .fullscreen-table {
            height: 100% !important;
        }
        
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .btn-success {
            background: #67c23a;
            color: white;
        }
        
        .btn-success:hover {
            background: #5daf34;
            transform: translateY(-1px);
        }
        
        .btn-danger {
            background: #f56c6c;
            color: white;
        }
        
        .btn-danger:hover {
            background: #f45656;
            transform: translateY(-1px);
        }
        
        .status-info {
            background: #e1f3d8;
            border: 1px solid #67c23a;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
        
        .error-info {
            background: #fef0f0;
            border: 1px solid #f56c6c;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            color: #f56c6c;
        }
    </style>
</head>
<body>
    <div id="app" class="container" :class="{ 'fullscreen-mode': isFullscreen }">
        <h1>🖥️ 浏览器全屏功能测试</h1>
        
        <!-- 普通模式下的工具栏 -->
        <div v-if="!isFullscreen">
            <button class="btn-success" @click="toggleFullscreen">
                📺 进入全屏模式
            </button>
            
            <div class="status-info">
                <strong>当前状态：</strong>
                <ul>
                    <li>应用全屏状态: {{ isFullscreen ? '是' : '否' }}</li>
                    <li>浏览器全屏状态: {{ isInBrowserFullscreen ? '是' : '否' }}</li>
                    <li>全屏API支持: {{ fullscreenSupported ? '是' : '否' }}</li>
                </ul>
            </div>
            
            <div v-if="errorMessage" class="error-info">
                {{ errorMessage }}
            </div>
            
            <h3>功能说明：</h3>
            <ul>
                <li>✅ 点击全屏按钮会同时触发应用全屏和浏览器全屏</li>
                <li>✅ 支持 ESC 键退出全屏</li>
                <li>✅ 支持 F11 键切换浏览器全屏</li>
                <li>✅ 自动同步浏览器全屏状态变化</li>
                <li>✅ 兼容各种浏览器的全屏API</li>
            </ul>
        </div>
        
        <!-- 全屏模式下的工具栏 -->
        <div v-if="isFullscreen" class="fullscreen-toolbar">
            <button class="btn-danger" @click="toggleFullscreen">
                ❌ 退出全屏
            </button>
        </div>
        
        <!-- 模拟的表格容器 -->
        <div class="table-container" :class="{ 'fullscreen-table': isFullscreen }">
            <div style="text-align: center;">
                <h2>{{ isFullscreen ? '🎯 全屏模式下的 VXE Table' : '📊 普通模式下的 VXE Table' }}</h2>
                <p><strong>应用状态:</strong> {{ isFullscreen ? '全屏' : '普通' }}</p>
                <p><strong>浏览器状态:</strong> {{ isInBrowserFullscreen ? '全屏' : '普通' }}</p>
                <p v-if="isFullscreen">💡 按 ESC 键或点击右上角按钮退出全屏</p>
                <p v-else>💡 点击全屏按钮体验浏览器原生全屏效果</p>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script>
        new Vue({
            el: '#app',
            data: {
                isFullscreen: false,
                isInBrowserFullscreen: false,
                fullscreenSupported: false,
                errorMessage: ''
            },
            mounted() {
                this.checkFullscreenSupport();
                this.addEventListeners();
                this.updateBrowserFullscreenStatus();
            },
            beforeDestroy() {
                this.removeEventListeners();
            },
            methods: {
                checkFullscreenSupport() {
                    this.fullscreenSupported = !!(
                        document.documentElement.requestFullscreen ||
                        document.documentElement.mozRequestFullScreen ||
                        document.documentElement.webkitRequestFullscreen ||
                        document.documentElement.msRequestFullscreen
                    );
                },
                
                addEventListeners() {
                    document.addEventListener('keydown', this.handleKeydown);
                    document.addEventListener('fullscreenchange', this.handleFullscreenChange);
                    document.addEventListener('mozfullscreenchange', this.handleFullscreenChange);
                    document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange);
                    document.addEventListener('msfullscreenchange', this.handleFullscreenChange);
                },
                
                removeEventListeners() {
                    document.removeEventListener('keydown', this.handleKeydown);
                    document.removeEventListener('fullscreenchange', this.handleFullscreenChange);
                    document.removeEventListener('mozfullscreenchange', this.handleFullscreenChange);
                    document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange);
                    document.removeEventListener('msfullscreenchange', this.handleFullscreenChange);
                },
                
                async toggleFullscreen() {
                    this.errorMessage = '';
                    
                    if (!this.isFullscreen) {
                        try {
                            await this.enterBrowserFullscreen();
                            this.isFullscreen = true;
                            document.body.style.overflow = 'hidden';
                        } catch (error) {
                            this.errorMessage = '浏览器全屏失败: ' + error.message;
                            console.warn('浏览器全屏失败，使用应用内全屏:', error);
                            this.isFullscreen = true;
                            document.body.style.overflow = 'hidden';
                        }
                    } else {
                        this.exitBrowserFullscreen();
                        this.isFullscreen = false;
                        document.body.style.overflow = '';
                    }
                },
                
                enterBrowserFullscreen() {
                    const element = document.documentElement;
                    
                    if (element.requestFullscreen) {
                        return element.requestFullscreen();
                    } else if (element.mozRequestFullScreen) {
                        return element.mozRequestFullScreen();
                    } else if (element.webkitRequestFullscreen) {
                        return element.webkitRequestFullscreen();
                    } else if (element.msRequestFullscreen) {
                        return element.msRequestFullscreen();
                    } else {
                        return Promise.reject(new Error('浏览器不支持全屏API'));
                    }
                },
                
                exitBrowserFullscreen() {
                    if (document.exitFullscreen) {
                        document.exitFullscreen();
                    } else if (document.mozCancelFullScreen) {
                        document.mozCancelFullScreen();
                    } else if (document.webkitExitFullscreen) {
                        document.webkitExitFullscreen();
                    } else if (document.msExitFullscreen) {
                        document.msExitFullscreen();
                    }
                },
                
                updateBrowserFullscreenStatus() {
                    this.isInBrowserFullscreen = !!(
                        document.fullscreenElement ||
                        document.mozFullScreenElement ||
                        document.webkitFullscreenElement ||
                        document.msFullscreenElement
                    );
                },
                
                handleKeydown(event) {
                    if (event.key === 'Escape' && this.isFullscreen) {
                        this.toggleFullscreen();
                    }
                },
                
                handleFullscreenChange() {
                    this.updateBrowserFullscreenStatus();
                    
                    // 如果浏览器退出了全屏，但应用还在全屏状态，则同步退出应用全屏
                    if (!this.isInBrowserFullscreen && this.isFullscreen) {
                        this.isFullscreen = false;
                        document.body.style.overflow = '';
                    }
                }
            }
        });
    </script>
</body>
</html>
