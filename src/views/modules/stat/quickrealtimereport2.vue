<template>
  <div class="mod-config" :class="{ 'fullscreen-mode': isFullscreen }">
    <canvas ref="canvas" style="display: none"></canvas>
    <SearchBar v-if="!isFullscreen" :options="searchBarOptions" @searchData="searchData">
      <el-button
        type="primary"
        :loading="exportLoading"
        @click="handleExportExcel"
      >
        导出数据
      </el-button>
      <el-button
        type="primary"
        :loading="downImgLoading"
        @click="handleDomToImage"
      >
        截图
      </el-button>
      <el-button
        type="warning"
        :disabled="dataListSelections.length === 0"
        @click="handleBatchUpdate"
      >
        修改回传 ({{ dataListSelections.length }})
      </el-button>
      <el-button
        type="info"
        @click="showColumnControl = !showColumnControl"
        icon="el-icon-setting"
      >
        列设置
      </el-button>
      <el-button
        type="success"
        @click="toggleFullscreen"
        :icon="isFullscreen ? 'el-icon-close' : 'el-icon-full-screen'"
      >
        {{ isFullscreen ? '退出全屏' : '全屏' }}
      </el-button>
    </SearchBar>
    <!-- 列控制面板 -->
    <el-collapse-transition>
      <div v-show="showColumnControl && !isFullscreen" class="column-control-panel">
        <el-card>
          <div slot="header" class="clearfix">
            <span>表格列显示设置</span>
            <div style="float: right;">
              <el-dropdown @command="applyPreset" style="margin-right: 10px;">
                <el-button type="text" size="small">
                  快速预设<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="essential">核心数据</el-dropdown-item>
                  <el-dropdown-item command="cost">投放数据</el-dropdown-item>
                  <el-dropdown-item command="conversion">转化数据</el-dropdown-item>
                  <el-dropdown-item command="revenue">变现数据</el-dropdown-item>
                  <el-dropdown-item command="all">全部显示</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-button style="padding: 3px 0" type="text" @click="resetColumns">重置</el-button>
            </div>
          </div>
          <div class="column-groups">
            <div class="column-group" v-for="group in columnGroups" :key="group.name">
              <h4>{{ group.label }}</h4>
              <el-checkbox-group v-model="group.visible">
                <el-checkbox
                  v-for="col in group.columns"
                  :key="col.key"
                  :label="col.key"
                  class="column-checkbox"
                >
                  {{ col.label }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </el-card>
      </div>
    </el-collapse-transition>
    <data-filter v-if="!isFullscreen" @update-filters="handleFilterUpdate" />

    <div
      class="mobile-card"
      v-if="isMobile"
    >
      <el-card v-for="data in filteredDataList" :key="data.id" class="mobile-card-item">
        <div slot="header" class="clearfix">
          <span>{{ data.accountName }}</span>
          <span class="header-date">{{data.dt}}</span>
        </div>
        <div class="text item"><strong>投放平台：</strong>{{data.channel}}</div>
        <div class="text item"><strong>账户ID：</strong>{{data.accountId}}</div>
        <div class="text item"><strong>余额：</strong>{{data.totalBalance}}</div>
        <div class="text item"><strong>预算：</strong>{{data.budget}}</div>
        <div class="text item"><strong>消耗：</strong>{{data.cost}}</div>
        <div class="text item"><strong>曝光：</strong>{{data.costExposureNum}}</div>
        <div class="text item"><strong>点击：</strong>{{data.adClickNum}}</div>
        <div class="text item"><strong>ECPM：</strong>{{data.costEcpm}}</div>
        <div class="text item"><strong>CPC：</strong>{{data.cpc}}</div>
        <div class="text item"><strong>CTR：</strong>{{toPercentage(data.ctr)}}</div>
        <div class="text item"><strong>CVR：</strong>{{toPercentage2(data.conversionRate)}}</div>
        <div class="text item"><strong>拉起率：</strong>{{toPercentage(data.landpagePercent)}} {{toPercentage(data.landpageNoPercent)}} {{toPercentage(data.dayUpPercent)}}</div>
        <div class="text item"><strong>吊起成本：</strong>去重 {{data.upCpi}} 不去重 {{data.upNoCpi}}</div>
        <div class="text item"><strong>新增用户：</strong>去重 {{data.newNum}} 多包 {{data.allNewNum}}</div>
        <div class="text item"><strong>人均拉起：</strong>{{data.upAvg}}</div>
        <div class="text item"><strong>人均拉回：</strong>{{data.backUpNum}}</div>
        <div class="text item"><strong>回传率：</strong>{{toPercentage2(data.returnRate)}}</div>
        <div class="text item"><strong>预估收益：</strong>{{data.income}}</div>
        <div class="text item"><strong>开屏收入：</strong>{{data.xincome}}</div>
        <div class="text item"><strong>总收入：</strong>{{data.zong}}</div>
        <div class="text item"><strong>曝光总数：</strong>{{data.exposureNum}}</div>
        <div class="text item"><strong>IPV：</strong>{{data.ipu}}</div>
        <div class="text item"><strong>CTR：</strong>{{toPercentage(data.inCtr)}}</div>
        <div class="text item"><strong>归因率：</strong>{{toPercentage(data.attrRate)}}</div>
        <div class="text item"><strong>ECPM：</strong>{{data.ecpm}}</div>
        <div class="text item"><strong>ROI：</strong>{{data.roi}}</div>
      </el-card>
    </div>
    <!-- 全屏模式下的工具栏 -->
    <div v-if="isFullscreen" class="fullscreen-toolbar">
      <el-button
        type="danger"
        size="small"
        @click="toggleFullscreen"
        icon="el-icon-close"
      >
        退出全屏
      </el-button>
    </div>

    <vxe-table
      v-if="!isMobile"
      ref="myTable"
      :key="tableKey"
      class="adapter-height"
      :class="{ 'fullscreen-table': isFullscreen }"
      :data="filteredDataList"
      :height="isFullscreen ? fullscreenTableHeight : tableHeight"
      border
      :loading="dataListLoading"
      :cell-style="cellStyle"
      :header-cell-style="{ background: '#f8f9fa' }"
      :row-config="{ isHover: true }"
      :scroll-y="{ enabled: true }"
      :scroll-x="{ enabled: true }"
      show-overflow
      @checkbox-change="selectionChangeHandle"
      style="width: 100%;"
    >
      <vxe-column
        type="checkbox"
        title="选择"
        width="50"
        align="center"
        fixed="left"
        header-align="center"
      />
      <vxe-column
        field="dt"
        title="日期"
        width="85"
        fixed="left"
        align="center"
        header-align="center"
      />
      <vxe-column
        field="accountId"
        title="账户ID"
        width="95"
        fixed="left"
        align="center"
        header-align="center"
      />
      <vxe-column
        v-if="isColumnVisible('accountName')"
        field="accountName"
        title="账户名称"
        width="90"
        align="center"
        header-align="center"
      />
      <vxe-column
        v-if="isColumnVisible('remark')"
        field="remark"
        title="备注"
        width="80"
        align="center"
        header-align="center"
      />
      <vxe-column
        v-if="false"
        field="brand"
        title="厂商"
        width="60"
        align="center"
        header-align="center"
      />
      <vxe-column
        v-if="isColumnVisible('channel')"
        field="channel"
        title="投放平台"
        width="85"
        align="center"
        header-align="center"
      />
      <vxe-column
        v-if="isColumnVisible('appId')"
        field="appId"
        title="应用ID"
        width="75"
        align="center"
        header-align="center"
      >
        <template #default="{ row }">
          <span>{{ getAppName(row.appId) }}</span>
        </template>
      </vxe-column>
      <vxe-column
        v-if="isColumnVisible('unionType')"
        field="unionType"
        title="投放媒体"
        width="85"
        align="center"
        header-align="center"
      >
        <template #default="{ row }">
          <div>{{ unionTypeMap[row.unionType] }}</div>
        </template>
      </vxe-column>
      <vxe-column
        v-if="isColumnVisible('agent')"
        field="agent"
        title="代理商"
        width="75"
        align="center"
        header-align="center"
      >
        <template #default="{ row }">
          <div>{{ agentMap[row.agent] }}</div>
        </template>
      </vxe-column>
      <vxe-column
        v-if="isColumnVisible('totalBalance')"
        field="totalBalance"
        title="余额"
        width="70"
        align="center"
        header-align="center"
      />
      <vxe-column
        v-if="isColumnVisible('budget')"
        field="budget"
        title="预算"
        width="90"
        align="center"
        header-align="center"
      >
        <template #default="{ row }">
          <div class="budget-cell" @mouseenter="showBudgetEdit = row.accountId" @mouseleave="showBudgetEdit = null">
            <span>{{ row.budget }}</span>
            <i
              v-show="showBudgetEdit === row.accountId"
              class="el-icon-edit budget-edit-icon"
              @click="setBudgetHandle(row)"
              title="设置日预算"
            ></i>
          </div>
        </template>
      </vxe-column>
      <vxe-column
        v-if="isColumnVisible('cost')"
        field="cost"
        title="消耗"
        width="70"
        align="center"
        header-align="center"
      >
        <template #default="{ row }">
          <div>{{ row.cost > 0 ? row.cost : '' }}</div>
        </template>
      </vxe-column>
      <vxe-column
        v-if="isColumnVisible('costExposureNum')"
        field="costExposureNum"
        title="曝光"
        width="75"
        align="center"
        header-align="center"
      />
      <vxe-column
        v-if="isColumnVisible('adClickNum')"
        field="adClickNum"
        title="点击"
        width="65"
        align="center"
        header-align="center"
      >
        <template #default="{ row }">
          <div>{{ row.adClickNum > 0 ? row.adClickNum : '' }}</div>
        </template>
      </vxe-column>
      <vxe-column
        v-if="isColumnVisible('costEcpm')"
        field="costEcpm"
        title="ECPM"
        width="70"
        align="center"
        header-align="center"
      />
      <vxe-column
        v-if="isColumnVisible('cpc')"
        field="cpc"
        title="CPC"
        width="65"
        align="center"
        header-align="center"
      >
        <template #default="{ row }">
          <div>{{ row.cpc > 0 ? row.cpc : '' }}</div>
        </template>
      </vxe-column>
      <vxe-column
        v-if="isColumnVisible('ctr')"
        field="ctr"
        title="CTR"
        width="65"
        align="center"
        header-align="center"
      >
        <template #default="{ row }">
          <div>{{ toPercentage(row.ctr) }}</div>
        </template>
      </vxe-column>
      <vxe-column
        v-if="isColumnVisible('cpa')"
        field="cpa"
        title="CPA"
        width="65"
        align="center"
        header-align="center"
      />
      <vxe-column
        v-if="isColumnVisible('conversionRate')"
        field="conversionRate"
        title="CVR"
        width="65"
        align="center"
        header-align="center"
      >
        <template #default="{ row }">
          <div>{{ toPercentage2(row.conversionRate) }}</div>
        </template>
      </vxe-column>
      <vxe-column
        v-if="isColumnVisible('landpagePercent')"
        field="landpagePercent"
        title="拉起率"
        width="70"
        align="center"
        header-align="center"
      >
        <template #header>
          <span>拉起率</span><br />
          <el-tooltip content="落地页拉起总次数（3分钟去重）/广告点击总次数" placement="top">
            <i class="el-icon-question" />
          </el-tooltip>
        </template>
        <template #default="{ row }">
          <div>{{ toPercentage(row.landpagePercent) }}</div>
        </template>
      </vxe-column>
      <vxe-column
        v-if="isColumnVisible('landpageNoPercent')"
        field="landpageNoPercent"
        title="拉起率2"
        width="75"
        align="center"
        header-align="center"
      >
        <template #header>
          <span>拉起率2</span><br />
          <el-tooltip content="落地页拉起的用户次数/广告点击总次数" placement="top">
            <i class="el-icon-question" />
          </el-tooltip>
        </template>
        <template #default="{ row }">
          <div>{{ toPercentage(row.landpageNoPercent) }}</div>
        </template>
      </vxe-column>
      <vxe-column
        v-if="isColumnVisible('dayUpPercent')"
        field="dayUpPercent"
        title="拉起率3"
        width="75"
        align="center"
        header-align="center"
      >
        <template #header>
          <span>拉起率3</span><br />
          <el-tooltip content="落地页拉起的用户次数(全天去重)/广告点击总次数" placement="top">
            <i class="el-icon-question" />
          </el-tooltip>
        </template>
        <template #default="{ row }">
          <div>{{ toPercentage(row.dayUpPercent) }}</div>
        </template>
      </vxe-column>
      <vxe-column
        v-if="isColumnVisible('upCpi')"
        field="upCpi"
        title="吊起成本(去重)"
        width="80"
        align="center"
        header-align="center"
      >
        <template #header>
          <Tooltip spanText="吊起成本(去重)" tooltipContent="累计成本/累计人数（三分钟去重）" />
        </template>
      </vxe-column>
      <vxe-column
        v-if="isColumnVisible('upNoCpi')"
        field="upNoCpi"
        title="吊起成本(不去重)"
        width="80"
        align="center"
        header-align="center"
      >
        <template #header>
          <Tooltip spanText="吊起成本(不去重)" tooltipContent="累计成本/累计人数（不去重）" />
        </template>
      </vxe-column>
      <vxe-column
        v-if="isColumnVisible('newNum')"
        field="newNum"
        title="新增用户(去重)"
        width="70"
        align="center"
        header-align="center"
      />
      <vxe-column
        v-if="isColumnVisible('allNewNum')"
        field="allNewNum"
        title="新增用户(多包)"
        width="70"
        align="center"
        header-align="center"
      >
        <template #header>
          <span>新增用户(多包)</span><br />
          <el-tooltip content="三分钟内，用户只要拉起过其他的包，就计算1" placement="top">
            <i class="el-icon-question" />
          </el-tooltip>
        </template>
      </vxe-column>
      <vxe-column
        v-if="isColumnVisible('upAvg')"
        field="upAvg"
        title="人均拉起"
        width="70"
        align="center"
        header-align="center"
      >
        <template #header>
          <span>人均拉起</span><br />
          <el-tooltip content="累计启动快应用次数/累计人数（三分钟去重）" placement="top">
            <i class="el-icon-question" />
          </el-tooltip>
        </template>
      </vxe-column>
      <vxe-column
        v-if="isColumnVisible('backUpNum')"
        field="backUpNum"
        title="人均拉回"
        width="70"
        align="center"
        header-align="center"
      >
        <template #header>
          <span>人均拉回</span><br />
          <el-tooltip content="拉回次数/注册成功人数（不去重）" placement="top">
            <i class="el-icon-question" />
          </el-tooltip>
        </template>
      </vxe-column>
      <vxe-column
        v-if="isColumnVisible('returnRate')"
        field="returnRate"
        title="回传率"
        width="70"
        align="center"
        header-align="center"
      >
        <template #default="{ row }">
          <div>{{ toPercentage2(row.returnRate) }}</div>
        </template>
      </vxe-column>
      <vxe-column
        v-if="isColumnVisible('income')"
        field="income"
        title="预估收益"
        width="80"
        align="center"
        header-align="center"
      />
      <vxe-column
        v-if="isColumnVisible('xincome')"
        field="xincome"
        title="开屏收入"
        width="80"
        align="center"
        header-align="center"
      />
      <vxe-column
        v-if="isColumnVisible('exposureNum')"
        field="exposureNum"
        title="曝光数"
        width="75"
        align="center"
        header-align="center"
      />
      <vxe-column
        v-if="isColumnVisible('ecpm')"
        field="ecpm"
        title="ECPM"
        width="70"
        align="center"
        header-align="center"
      />
      <vxe-column
        v-if="isColumnVisible('inCtr')"
        field="inCtr"
        title="CTR"
        width="65"
        align="center"
        header-align="center"
      >
        <template #default="{ row }">
          <div>{{ toPercentage(row.inCtr) }}</div>
        </template>
      </vxe-column>
      <vxe-column
        v-if="isColumnVisible('ipu')"
        field="ipu"
        title="IPV"
        width="65"
        align="center"
        header-align="center"
      >
        <template #header>
          <Tooltip spanText="IPV" tooltipContent="实时广告展示/实时新增用户" />
        </template>
      </vxe-column>
      <vxe-column
        v-if="isColumnVisible('reExposureNum')"
        field="reExposureNum"
        title="激励视频曝光"
        width="65"
        align="center"
        header-align="center"
      />
      <vxe-column
        v-if="isColumnVisible('reEcpm')"
        field="reEcpm"
        title="激励视频ECPM"
        width="65"
        align="center"
        header-align="center"
      />
      <vxe-column
        v-if="isColumnVisible('reCtr')"
        field="reCtr"
        title="激励视频CTR"
        width="65"
        align="center"
        header-align="center"
      >
        <template #default="{ row }">
          <div>{{ toPercentage(row.reCtr) }}</div>
        </template>
      </vxe-column>
      <vxe-column
        v-if="isColumnVisible('reIpu')"
        field="reIpu"
        title="激励视频IPV"
        width="65"
        align="center"
        header-align="center"
      />
      <vxe-column
        v-if="isColumnVisible('attrRate')"
        field="attrRate"
        title="归因率"
        width="70"
        align="center"
        header-align="center"
      >
        <template #header>
          <span>归因率</span>
          <el-tooltip content="归因人数/新增人数" placement="top">
            <i class="el-icon-question" />
          </el-tooltip>
        </template>
        <template #default="{ row }">
          <div>{{ toPercentage(row.attrRate) }}</div>
        </template>
      </vxe-column>
      <vxe-column
        field="zong"
        title="总收入"
        width="75"
        fixed="right"
        align="center"
        header-align="center"
      />
      <vxe-column
        field="roi"
        title="ROI"
        width="70"
        fixed="right"
        align="center"
        header-align="center"
      >
        <template #default="{ row }">
          <div :style="row.roi >= 1 ? 'color: #f56c6c' : 'color: #67c23a'">{{ row.roi }}</div>
        </template>
        <template #header>
          <Tooltip spanText="ROI" tooltipContent="实时预估收入/投放金额*100" />
        </template>
      </vxe-column>
      <vxe-column
        title="操作"
        min-width="200"
        fixed="right"
        align="center"
        header-align="center"
      >
        <template #default="{ row }">
          <el-button type="text" size="small" @click="planHandle(row)">广告组</el-button>
          <el-button type="text" size="small" @click="hourHandle(row)">分时</el-button>
          <el-button type="text" size="small" @click="addOrUpdateHandle(row)">修改</el-button>
          <el-button type="text" size="small" @click="ocpcHandle(row)">出价</el-button>
          <el-button type="text" size="small" @click="closeHandle(row)">拉空</el-button>
        </template>
      </vxe-column>
    </vxe-table>
    <el-pagination
      v-if="!isFullscreen"
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[20, 60, 100, 500, 1000]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    ></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    ></add-or-update>
    <new-update
      v-if="newUpdateVisible"
      ref="newUpdate"
      @refreshDataList="getDataList"
    ></new-update>
    <plan
      v-if="planVisible"
      ref="planDialog"
    ></plan>
    <hour
      v-if="hourVisible"
      ref="hourDialog"
    ></hour>
  </div>
</template>

<script>
import { DomToImageJpeg, generateDateTimeString } from '@/utils/domToImage'
import Decimal from 'decimal.js'
import AddOrUpdate from '../feedback/launchconfig-add-or-update.vue'
import NewUpdate from  './quickrealtimereport2-update.vue'
import Plan from './quickrealtimereport-plan'
import Hour from './quickrealtimereport-hour2'
import Tooltip from './components/tooltip.vue'
import SearchBar from './components/searchBar.vue'
import DataFilter from './components/data-filter.vue'
import dayjs from '@/dayjs'
import { exportExcel } from '@/utils/exportExcel'
import { getAppName } from '@/filters'
import { agentList } from '@/map/agent'

export default {
  data() {
    return {
      img1: '',
      img2: '',
      exportLoading: false,
      downImgLoading: false,
      minWidth: 80,
      tableHeight: '100%',
      dataForm: {
        key: '',
      },
      dataList: [
        {
          brand: '暂无数据',
          budget: 100.0,
        },
      ],
      pageIndex: 1,
      pageSize: 500,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      newUpdateVisible: false,
      planVisible: false,
      hourVisible: false,
      searchBarOptions: [],
      startTime: '',
      endTime: '',
      accountIds: '',
      isMobile: false,
      appId: '',
      unionType: '',
      channel: '',
      agent: '',
      isAgg: 0,
      agentMap: agentList.reduce((map, item) => {
        map[item.value] = item.label
        return map
      }, {}),
      unionTypeMap: {
        1: 'oppo联盟',
        2: 'oppo站内',
        3: 'vivo联盟',
        4: 'vivo站内',
        5: '快手站内',
        6: '快手联盟',
        7: '穿山甲',
        8: '抖音',
        9: '优量汇',
        10: 'uc浏览器',
        11: '荣耀站内',
        12: '荣耀联盟',
        13: '华为站内',
        14: '华为联盟',
        15: '小米站内',
        16: '小米联盟',
        17: '百度站内',
        18: '百度联盟',
      },
      filteredDataList: [],
      filterConditions: [], // 仅用于存储条件备查
      showColumnControl: false,
      showBudgetEdit: null, // 控制预算编辑图标显示
      tableKey: 0, // 表格强制刷新key
      isFullscreen: false, // 全屏状态
      fullscreenTableHeight: '100vh', // 全屏时表格高度
      columnGroups: [
        {
          name: 'basic',
          label: '基础信息',
          visible: ['accountName', 'remark', 'channel', 'appId', 'unionType', 'agent'],
          columns: [
            { key: 'accountName', label: '账户名称' },
            { key: 'remark', label: '备注' },
            { key: 'channel', label: '投放平台' },
            { key: 'appId', label: '应用ID' },
            { key: 'unionType', label: '投放媒体' },
            { key: 'agent', label: '代理商' }
          ]
        },
        {
          name: 'cost',
          label: '投放数据',
          visible: ['totalBalance', 'budget', 'cost', 'costExposureNum', 'adClickNum', 'costEcpm', 'cpc', 'ctr', 'cpa', 'conversionRate'],
          columns: [
            { key: 'totalBalance', label: '余额' },
            { key: 'budget', label: '预算' },
            { key: 'cost', label: '消耗' },
            { key: 'costExposureNum', label: '曝光' },
            { key: 'adClickNum', label: '点击' },
            { key: 'costEcpm', label: 'ECPM' },
            { key: 'cpc', label: 'CPC' },
            { key: 'ctr', label: 'CTR' },
            { key: 'cpa', label: 'CPA' },
            { key: 'conversionRate', label: 'CVR' }
          ]
        },
        {
          name: 'conversion',
          label: '转化数据',
          visible: ['landpagePercent', 'landpageNoPercent', 'dayUpPercent', 'upCpi', 'upNoCpi', 'newNum', 'allNewNum', 'upAvg', 'backUpNum', 'returnRate'],
          columns: [
            { key: 'landpagePercent', label: '拉起率' },
            { key: 'landpageNoPercent', label: '拉起率2' },
            { key: 'dayUpPercent', label: '拉起率3' },
            { key: 'upCpi', label: '吊起成本(去重)' },
            { key: 'upNoCpi', label: '吊起成本(不去重)' },
            { key: 'newNum', label: '新增用户(去重)' },
            { key: 'allNewNum', label: '新增用户(多包)' },
            { key: 'upAvg', label: '人均拉起' },
            { key: 'backUpNum', label: '人均拉回' },
            { key: 'returnRate', label: '回传率' }
          ]
        },
        {
          name: 'revenue',
          label: '变现数据',
          visible: ['income', 'xincome', 'exposureNum', 'ecpm', 'inCtr', 'ipu', 'reExposureNum', 'reEcpm', 'reCtr', 'reIpu', 'attrRate'],
          columns: [
            { key: 'income', label: '预估收益' },
            { key: 'xincome', label: '开屏收入' },
            { key: 'exposureNum', label: '曝光数' },
            { key: 'ecpm', label: 'ECPM' },
            { key: 'inCtr', label: 'CTR' },
            { key: 'ipu', label: 'IPV' },
            { key: 'reExposureNum', label: '激励视频曝光' },
            { key: 'reEcpm', label: '激励视频ECPM' },
            { key: 'reCtr', label: '激励视频CTR' },
            { key: 'reIpu', label: '激励视频IPV' },
            { key: 'attrRate', label: '归因率' }
          ]
        }
      ]
    }
  },
  components: {
    AddOrUpdate,
    NewUpdate,
    Plan,
    Hour,
    Tooltip,
    SearchBar,
    DataFilter
  },
  activated() {
    this.changleTableHeight()
    window.addEventListener('resize', this.changleTableHeight)
    document.addEventListener('keydown', this.handleKeydown)
    // 监听浏览器全屏状态变化
    document.addEventListener('fullscreenchange', this.handleFullscreenChange)
    document.addEventListener('mozfullscreenchange', this.handleFullscreenChange)
    document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange)
    document.addEventListener('msfullscreenchange', this.handleFullscreenChange)
    this.getDataList()
  },
  deactivated() {
    window.removeEventListener('resize', this.changleTableHeight)
    document.removeEventListener('keydown', this.handleKeydown)
    // 移除浏览器全屏状态监听
    document.removeEventListener('fullscreenchange', this.handleFullscreenChange)
    document.removeEventListener('mozfullscreenchange', this.handleFullscreenChange)
    document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange)
    document.removeEventListener('msfullscreenchange', this.handleFullscreenChange)
  },
  watch: {
    dataList: {
      handler() {
        this.filterDataList();
      },
      immediate: true
    },
    columnGroups: {
      handler() {
        // 延迟执行，确保DOM更新完成
        this.$nextTick(() => {
          this.forceTableRefresh();
        });
      },
      deep: true
    }
  },
  methods: {
    getAppName,
    changleTableHeight() {
      this.isMobile = window.innerWidth < 600
      let modConfig = document.querySelector('.mod-config')
      let vxeTable = document.querySelector('.adapter-height')
      if (!vxeTable) return
      if (window.innerWidth > 600 && window.innerWidth < 1244) {
        let scale = window.innerWidth / 1244
        modConfig.style.height = `${Math.max((window.innerHeight - 135) / scale, 600)}px`
        modConfig.style.transform = `scale(${scale})`
        modConfig.style.transformOrigin = 'left top'
        vxeTable.style.flexGrow = ''
        this.tableHeight = (window.innerHeight - 262) / scale
      } else {
        let modConfigHeight = Math.max(window.innerHeight - 135, 600)
        if (this.isMobile) {
          modConfig.style.height = 'auto'
        } else {
          modConfig.style.height = `${modConfigHeight}px`
        }
        modConfig.style.transform = ''
        vxeTable.style.flexGrow = '1'
        this.tableHeight = modConfigHeight - 186
      }
    },
    async handleDomToImage() {
      this.downImgLoading = true
      const table = this.$refs.myTable.$el
      const table_body = table.querySelector('.vxe-table--body-wrapper table')
      const table_header = table.querySelector(
        '.vxe-table--header-wrapper table'
      )
      const imgUrl1 = await DomToImageJpeg(table_header)
      this.img1 = imgUrl1
      const imgUrl2 = await DomToImageJpeg(table_body)
      this.img2 = imgUrl2

      await this.DownloadImg(this.img1, this.img2)
      this.downImgLoading = false
    },

    async DownloadImg(img1, img2) {
      const canvas = this.$refs.canvas
      const ctx = canvas.getContext('2d')

      try {
        const image1 = await this.loadImage(img1)
        const image2 = await this.loadImage(img2)

        const width = Math.max(image1.width, image2.width)
        const height = image1.height + image2.height
        canvas.width = width
        canvas.height = height
        ctx.drawImage(image1, 0, 0)
        ctx.drawImage(image2, 0, image1.height)

        const dataURL = canvas.toDataURL('image/jpeg')
        this.downloadImage(dataURL)
      } catch (error) {
        console.error('图片处理失败:', error)
      }
    },

    loadImage(src) {
      return new Promise((resolve, reject) => {
        const img = new Image()
        img.crossOrigin = 'anonymous'
        img.src = src
        img.onload = () => resolve(img)
        img.onerror = err => reject(err)
      })
    },

    downloadImage(dataURL) {
      const a = document.createElement('a')
      a.href = dataURL
      a.download = `快应用实时报表-${generateDateTimeString()}`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
    },

    async handleExportExcel() {
      try {
        this.exportLoading = true
        
        // 检查是否有数据
        if (!this.filteredDataList || this.filteredDataList.length === 0) {
          this.$message.warning('没有数据可导出')
          return
        }
        
        // 生成表头配置
        const headers = this.generateHeaders()
        
        // 使用 exportExcel 函数导出数据
        await exportExcel(this.filteredDataList, headers)
        
        this.$message.success('导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败: ' + (error.message || '未知错误'))
      } finally {
        this.exportLoading = false
      }
    },
    
    generateHeaders() {
      // 根据当前可见的列生成表头配置
      const headers = []
      
      // 添加固定的列
      headers.push({ title: '日期', dataKey: 'dt' })
      headers.push({ title: '账户ID', dataKey: 'accountId' })
      
      // 根据列组配置添加可见的列
      this.columnGroups.forEach(group => {
        group.columns.forEach(col => {
          if (group.visible.includes(col.key)) {
            // 为特殊字段添加数据格式化
            let dataKey = col.key
            if (['ctr', 'conversionRate', 'landpagePercent', 'landpageNoPercent', 'dayUpPercent', 'returnRate', 'inCtr', 'reCtr', 'attrRate'].includes(col.key)) {
              dataKey = (item) => {
                const value = item[col.key]
                if (col.key === 'conversionRate' || col.key === 'returnRate') {
                  return this.toPercentage2(value)
                }
                return this.toPercentage(value)
              }
            } else if (col.key === 'unionType') {
              dataKey = (item) => this.unionTypeMap[item[col.key]] || item[col.key]
            } else if (col.key === 'agent') {
              dataKey = (item) => this.agentMap[item[col.key]] || item[col.key]
            } else if (col.key === 'appId') {
              dataKey = (item) => this.getAppName(item[col.key])
            }
            headers.push({ title: col.label, dataKey: dataKey })
          }
        })
      })
      
      // 添加固定的右侧列
      headers.push({ title: '总收入', dataKey: 'zong' })
      headers.push({ title: 'ROI', dataKey: 'roi' })
      
      return headers
    },

    searchData(formInline) {
      this.accountIds = formInline.accountIds.join()
      if (formInline.date) {
        const format = 'YYYY-MM-DD'
        this.startTime = formInline.date[0]
          ? dayjs(formInline.date[0]).format(format)
          : ''
        this.endTime = formInline.date[1]
          ? dayjs(formInline.date[1]).format(format)
          : ''
      } else {
        this.startTime = ''
        this.endTime = ''
      }
      this.appId = formInline.appId
      this.unionType = formInline.unionType
      this.channel = formInline.channel
      this.agent = formInline.agent
      this.isAgg = formInline.isAgg
      this.getDataList()
    },
    // 获取数据列表
    getDataList() {
      this.dataListSelections = []
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/stat/quickrealtimereport/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          accountIds: this.accountIds,
          startTime: this.startTime,
          endTime: this.endTime,
          appId: this.appId,
          unionType: this.unionType,
          channel: this.channel,
          agent: this.agent,
          isAgg: this.isAgg,
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
            .map(it => {
              if (it.adClickNum > 0) {
                it.cpa = (it.cost / (it.adClickNum * it.conversionRate)).toFixed(2)
              }
              it.zong = this.addEarnings(it.income, it.xincome)
              return it
            })
          this.totalPage = data.page.totalCount

          this.searchBarOptions = [...new Set(this.dataList.map(it => it.accountId))]

        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    toPercentage(value = 0) {
      return `${Math.round(value * 100)}%`
    },

    toPercentage2(value) {
      return `${(value * 100).toFixed(2)}%`
    },

    addEarnings(a, b) {
      if (!a && !b) {
        return 0
      }
      if (!b) {
        return a.toFixed(2)
      }
      return new Decimal(a).plus(b).toNumber().toFixed(2)
    },

    cellStyle(data) {
      if (data.rowIndex % 2 === 1) {
        return { 'background-color': '#F7FDFB !important', padding: 0 }
      }
      return { padding: 0 }
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle() {
      // 使用 getCheckboxRecords 方法获取选中的行数据
      this.dataListSelections = this.$refs.myTable.getCheckboxRecords()
    },
    // 新增 / 修改
    addOrUpdateHandle(row) {
      this.$http({
        url: this.$http.adornUrl('/activate/activaterules/get_id'),
        method: 'get',
        params: this.$http.adornParams({
          appId: row.appId,
          accountId: row.accountId,
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.addOrUpdateVisible = true
          this.$nextTick(() => {
            this.$refs.addOrUpdate.init(data.data.id)
          })
        }
      })
    },
    // 新增 / 修改
    addOrUpdateHandleNew(accountIds) {
      this.newUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.newUpdate.init(accountIds)
      })
    },
    // 批量修改
    handleBatchUpdate() {
      if (this.dataListSelections.length === 0) {
        this.$message.warning('请先选择要修改的行')
        return
      }

      // 获取选中行的 accountId
      const accountIds = this.dataListSelections.map(row => row.accountId).join(',')

      // 调用批量修改方法
      this.addOrUpdateHandleNew(accountIds)
    },
    planHandle(row) {
      this.planVisible = true
      this.$nextTick(() => {
        this.$refs.planDialog.init(row)
      })
    },
    hourHandle(row) {
      this.hourVisible = true
      this.$nextTick(() => {
        this.$refs.hourDialog.init(row)
      })
    },
    ocpcHandle(row) {
      this.$prompt('请输入需要修改的转化出价', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: '',
        inputPlaceholder: '转化出价'
      }).then(({ value }) => {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/stat/quickrealtimereport/group_list'),
          method: 'get',
          params: this.$http.adornParams({
            page: 1,
            limit: 1000,
            accountId: row.accountId,
            dt: row.dt
          }),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$http({
              url: this.$http.adornUrl('/stat/quickrealtimereport/edit_ocpc'),
              method: 'get',
              params: this.$http.adornParams({
                accountId: row.accountId,
                groupId: data.list.map(it => it.groupId).join(','),
                ocpc: value,
              }),
            }).then(({ data }) => {
              if (data.data && data.data.ret === 0) {
                this.$message({
                  type: 'success',
                  message: '操作成功',
                })
              } else {
                this.$message({
                  type: 'error',
                  message: data.data.msg || '操作失败',
                })
              }
              this.dataListLoading = false
            })
          } else {
            this.dataListLoading = false
          }
        })
      }).catch(() => {
      });
    },
    setBudgetHandle(row) {
      this.$prompt('请输入需要设置的日预算金额', '设置日预算', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: '',
        inputPlaceholder: '日预算金额（元）',
        inputPattern: /^\d+(\.\d{1,2})?$/,
        inputErrorMessage: '请输入有效的金额格式'
      }).then(({ value }) => {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/stat/quickrealtimereport/set_acc_day_budget'),
          method: 'get',
          params: this.$http.adornParams({
            accountId: row.accountId,
            accDayBudget: value * 100,
          }),
        }).then(({ data }) => {
          if (data.data && data.data.ret === 0) {
            this.$message({
              type: 'success',
              message: '日预算设置成功',
            })
          } else {
            this.$message({
              type: 'error',
              message: data.msg || '日预算设置失败',
            })
          }
          this.dataListLoading = false
        }).catch((error) => {
          this.$message({
            type: 'error',
            message: '网络错误，请稍后重试',
          })
          this.dataListLoading = false
        })
      }).catch(() => {
      });
    },
    closeHandle(row) {
      this.$confirm('确定要拉空该账户吗', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/stat/quickrealtimereport/edit_schedule'),
          method: 'get',
          params: this.$http.adornParams({
            accountId: row.accountId,
          }),
        }).then(({ data }) => {
          if (data.data && data.data.ret === 0) {
            this.$message({
              type: 'success',
              message: '操作成功',
            })
          } else {
            this.$message({
              type: 'error',
              message: data.data.msg || '操作失败',
            })
          }
          this.dataListLoading = false
        })
      }).catch(() => {
      });
    },

    handleFilterUpdate(conditions) {
      this.filterConditions = conditions;
      this.filterDataList();
    },
    filterDataList() {
      if (!this.filterConditions.length) {
        this.filteredDataList = this.dataList.slice();
        return;
      }
      this.filteredDataList = this.dataList.filter(row => {
        return this.filterConditions.every(cond => {
          const val = Number(row[cond.field]);
          if (isNaN(val)) return false;
          switch (cond.op) {
            case '>=': return val >= cond.value;
            case '<=': return val <= cond.value;
            case '>': return val > cond.value;
            case '<': return val < cond.value;
            case '==': return val == cond.value;
            default: return true;
          }
        });
      });
    },

    // 列控制相关方法
    isColumnVisible(columnKey) {
      return this.columnGroups.some(group =>
        group.visible.includes(columnKey)
      );
    },

    // 强制刷新表格
    forceTableRefresh() {
      // 更新key强制重新渲染表格
      this.tableKey += 1;
      this.$nextTick(() => {
        if (this.$refs.myTable) {
          this.$refs.myTable.doLayout();
        }
      });
    },

    resetColumns() {
      // 重置为默认显示状态
      this.columnGroups.forEach(group => {
        group.visible = [...group.columns.map(col => col.key)];
      });
    },

    // 获取所有可见列的key
    getVisibleColumns() {
      const visible = [];
      this.columnGroups.forEach(group => {
        visible.push(...group.visible);
      });
      return visible;
    },

    // 应用预设
    applyPreset(command) {
      const presets = {
        essential: {
          basic: ['appId', 'unionType', 'agent'],
          cost: ['cost', 'costEcpm', 'cpc', 'ctr', 'cpa', 'conversionRate'],
          conversion: ['landpagePercent', 'landpageNoPercent', 'backUpNum'],
          revenue: ['ecpm', 'inCtr', 'ipu', 'reEcpm']
        },
        cost: {
          basic: ['channel'],
          cost: ['totalBalance', 'budget', 'cost', 'costExposureNum', 'adClickNum', 'costEcpm', 'cpc', 'ctr', 'cpa', 'conversionRate'],
          conversion: [],
          revenue: []
        },
        conversion: {
          basic: ['channel'],
          cost: ['cost', 'adClickNum'],
          conversion: ['landpagePercent', 'landpageNoPercent', 'dayUpPercent', 'upCpi', 'upNoCpi', 'newNum', 'allNewNum', 'upAvg', 'backUpNum', 'returnRate'],
          revenue: []
        },
        revenue: {
          basic: ['channel'],
          cost: ['cost'],
          conversion: ['newNum'],
          revenue: ['income', 'xincome', 'exposureNum', 'ecpm', 'inCtr', 'ipu', 'attrRate']
        },
        all: {
          basic: ['accountName', 'remark', 'channel', 'appId', 'unionType', 'agent'],
          cost: ['totalBalance', 'budget', 'cost', 'costExposureNum', 'adClickNum', 'costEcpm', 'cpc', 'ctr', 'cpa', 'conversionRate'],
          conversion: ['landpagePercent', 'landpageNoPercent', 'dayUpPercent', 'upCpi', 'upNoCpi', 'newNum', 'allNewNum', 'upAvg', 'backUpNum', 'returnRate'],
          revenue: ['income', 'xincome', 'exposureNum', 'ecpm', 'inCtr', 'ipu', 'reExposureNum', 'reEcpm', 'reCtr', 'reIpu', 'attrRate']
        }
      };

      const preset = presets[command];
      if (preset) {
        this.columnGroups.forEach(group => {
          group.visible = preset[group.name] || [];
        });
      }
      this.showColumnControl = false
    },



    // 自适应列宽
    autoResizeColumns() {
      this.$nextTick(() => {
        if (this.$refs.myTable) {
          this.$refs.myTable.doLayout();
        }
      });
    },

    // 全屏切换
    async toggleFullscreen() {
      if (!this.isFullscreen) {
        // 进入全屏模式
        try {
          // 先触发浏览器全屏
          await this.enterBrowserFullscreen();
          // 然后设置应用全屏状态
          this.isFullscreen = true;
          this.fullscreenTableHeight = window.innerHeight; // 浏览器全屏时使用整个屏幕高度
          document.body.style.overflow = 'hidden';
        } catch (error) {
          console.warn('浏览器全屏失败，使用应用内全屏:', error);
          // 如果浏览器全屏失败，仍然使用应用内全屏
          this.isFullscreen = true;
          this.fullscreenTableHeight = window.innerHeight - 60;
          document.body.style.overflow = 'hidden';
        }
      } else {
        // 退出全屏模式
        this.exitBrowserFullscreen();
        this.isFullscreen = false;
        document.body.style.overflow = '';
        this.changleTableHeight(); // 重新计算表格高度
      }

      // 强制刷新表格布局
      this.$nextTick(() => {
        this.forceTableRefresh();
      });
    },

    // 进入浏览器全屏
    enterBrowserFullscreen() {
      const element = document.documentElement;

      if (element.requestFullscreen) {
        return element.requestFullscreen();
      } else if (element.mozRequestFullScreen) {
        return element.mozRequestFullScreen();
      } else if (element.webkitRequestFullscreen) {
        return element.webkitRequestFullscreen();
      } else if (element.msRequestFullscreen) {
        return element.msRequestFullscreen();
      } else {
        return Promise.reject(new Error('浏览器不支持全屏API'));
      }
    },

    // 退出浏览器全屏
    exitBrowserFullscreen() {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen();
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
      }
    },

    // 检查是否处于浏览器全屏状态
    isInBrowserFullscreen() {
      return !!(
        document.fullscreenElement ||
        document.mozFullScreenElement ||
        document.webkitFullscreenElement ||
        document.msFullscreenElement
      );
    },

    // 键盘事件处理
    handleKeydown(event) {
      // ESC键退出全屏
      if (event.key === 'Escape' && this.isFullscreen) {
        this.toggleFullscreen();
      }
    },

    // 处理浏览器全屏状态变化
    handleFullscreenChange() {
      const isInBrowserFullscreen = this.isInBrowserFullscreen();

      // 如果浏览器退出了全屏，但应用还在全屏状态，则同步退出应用全屏
      if (!isInBrowserFullscreen && this.isFullscreen) {
        this.isFullscreen = false;
        document.body.style.overflow = '';
        this.changleTableHeight();

        this.$nextTick(() => {
          this.forceTableRefresh();
        });
      }
      // 如果浏览器进入全屏，调整表格高度
      else if (isInBrowserFullscreen && this.isFullscreen) {
        this.fullscreenTableHeight = window.innerHeight;
        this.$nextTick(() => {
          this.forceTableRefresh();
        });
      }
    },
  },
}
</script>
<style scoped>
.mod-config {
  width: 100%;
  height: calc(100vh - 135px);
  display: flex;
  flex-direction: column;
}
.adapter-height {
  flex-grow: 1;
  overflow-x: auto;
}
.vxe-table /deep/ th {
  padding: 0;
}

.vxe-table /deep/ td {
  padding: 1px;
}

::deep .el-card__body {
  padding: 0px;
}
.vxe-table /deep/ .vxe-cell {
  padding: 0;
  font-size: 11px;
}
.mobile-card-item {
  margin: 15px 0;
  width: 75vw;
  border-radius: 8px;
  overflow: hidden;
}

.header-date {
  float: right;
  font-size: 12px;
  color: #999;
}

.text.item {
  font-size: 13px;
  line-height: 1.2;
  padding: 3px 0;
}

/* 列控制面板样式 */
.column-control-panel {
  margin: 10px 0;
}

.column-groups {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.column-group {
  flex: 1;
  min-width: 200px;
}

.column-group h4 {
  margin: 0 0 10px 0;
  color: #409eff;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 5px;
}

.column-checkbox {
  display: block;
  margin: 8px 0;
  white-space: nowrap;
}

.column-checkbox .el-checkbox__label {
  font-size: 12px;
}

/* 预算列编辑图标样式 */
.budget-cell {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.budget-edit-icon {
  color: #409eff;
  cursor: pointer;
  font-size: 12px;
  transition: color 0.3s;
}

.budget-edit-icon:hover {
  color: #66b1ff;
}

/* 全屏模式样式 */
.fullscreen-mode {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999 !important;
  background: white !important;
  transform: none !important;
  overflow: hidden !important;
  padding: 0 !important;
  margin: 0 !important;
}

.fullscreen-toolbar {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10000;
  background: rgba(255, 255, 255, 0.95);
  padding: 8px 12px;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.fullscreen-table {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
}

/* 浏览器全屏时的额外样式 */
:fullscreen .fullscreen-mode,
:-webkit-full-screen .fullscreen-mode,
:-moz-full-screen .fullscreen-mode,
:-ms-fullscreen .fullscreen-mode {
  width: 100% !important;
  height: 100% !important;
}

/* 确保在浏览器全屏时表格占满整个屏幕 */
:fullscreen .fullscreen-table,
:-webkit-full-screen .fullscreen-table,
:-moz-full-screen .fullscreen-table,
:-ms-fullscreen .fullscreen-table {
  width: 100% !important;
  height: 100% !important;
}
</style>
