<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全屏功能测试</title>
    <style>
        .container {
            padding: 20px;
            border: 2px solid #ccc;
            margin: 20px;
        }
        
        .fullscreen-mode {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            z-index: 9999 !important;
            background: white !important;
            overflow: hidden !important;
            margin: 0 !important;
            padding: 20px !important;
        }
        
        .fullscreen-toolbar {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10000;
            background: rgba(255, 255, 255, 0.9);
            padding: 5px;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .table-container {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            background: #f9f9f9;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }
        
        .fullscreen-table {
            height: calc(100vh - 80px) !important;
        }
        
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .btn-success {
            background: #67c23a;
            color: white;
        }
        
        .btn-danger {
            background: #f56c6c;
            color: white;
        }
    </style>
</head>
<body>
    <div id="app" class="container" :class="{ 'fullscreen-mode': isFullscreen }">
        <h1>VXE Table 全屏功能测试</h1>
        
        <!-- 普通模式下的工具栏 -->
        <div v-if="!isFullscreen">
            <button class="btn-success" @click="toggleFullscreen">
                📺 全屏显示
            </button>
            <p>点击全屏按钮可以让表格全屏显示，按 ESC 键或点击退出按钮可以退出全屏。</p>
        </div>
        
        <!-- 全屏模式下的工具栏 -->
        <div v-if="isFullscreen" class="fullscreen-toolbar">
            <button class="btn-danger" @click="toggleFullscreen">
                ❌ 退出全屏
            </button>
        </div>
        
        <!-- 模拟的表格容器 -->
        <div class="table-container" :class="{ 'fullscreen-table': isFullscreen }">
            <div>
                <h2>{{ isFullscreen ? '全屏模式下的 VXE Table' : '普通模式下的 VXE Table' }}</h2>
                <p>当前状态: {{ isFullscreen ? '全屏' : '普通' }}</p>
                <p>{{ isFullscreen ? '按 ESC 键可以退出全屏' : '点击全屏按钮进入全屏模式' }}</p>
            </div>
        </div>
        
        <div v-if="!isFullscreen">
            <h3>功能说明：</h3>
            <ul>
                <li>✅ 全屏按钮：点击进入全屏模式</li>
                <li>✅ 退出全屏：点击右上角退出按钮或按 ESC 键</li>
                <li>✅ 隐藏其他元素：全屏时隐藏搜索栏、分页等</li>
                <li>✅ 表格自适应：全屏时表格占满整个屏幕</li>
            </ul>
        </div>
    </div>

    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script>
        new Vue({
            el: '#app',
            data: {
                isFullscreen: false
            },
            mounted() {
                document.addEventListener('keydown', this.handleKeydown);
            },
            beforeDestroy() {
                document.removeEventListener('keydown', this.handleKeydown);
            },
            methods: {
                toggleFullscreen() {
                    this.isFullscreen = !this.isFullscreen;
                    
                    if (this.isFullscreen) {
                        // 进入全屏模式
                        document.body.style.overflow = 'hidden';
                    } else {
                        // 退出全屏模式
                        document.body.style.overflow = '';
                    }
                },
                
                handleKeydown(event) {
                    // ESC键退出全屏
                    if (event.key === 'Escape' && this.isFullscreen) {
                        this.toggleFullscreen();
                    }
                }
            }
        });
    </script>
</body>
</html>
